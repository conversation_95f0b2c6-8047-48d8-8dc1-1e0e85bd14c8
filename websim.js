/**
 * Websim.js - Dual AI System Client
 * This file provides a compatible interface for the existing application
 * to work with both online (DeepSeek via OpenRouter) and offline (Llama 3.2 via Ollama) AI models
 */

// Store student stress data globally
let studentStressData = {
    lastPrediction: null,
    studentData: null
};

// AI System Status
let aiSystemStatus = {
    currentModel: 'unknown',
    onlineAvailable: false,
    offlineAvailable: false,
    preferOnline: true,
    lastUpdate: null
};

// Function to update student stress data
function updateStressData(prediction, data) {
    studentStressData.lastPrediction = prediction;
    studentStressData.studentData = data;
    console.log('Student stress data updated:', studentStressData);
}

// Function to get AI system status
async function getAISystemStatus() {
    try {
        const response = await fetch('http://localhost:3000/api/ai/status');
        if (response.ok) {
            const data = await response.json();
            aiSystemStatus = {
                ...aiSystemStatus,
                ...data.status,
                lastUpdate: Date.now()
            };
            console.log('AI System Status:', aiSystemStatus);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('aiStatusUpdated', {
                detail: { status: aiSystemStatus, models: data.models }
            }));

            return data;
        }
    } catch (error) {
        console.error('Failed to get AI system status:', error);
    }
    return null;
}

// Function to set AI preference
async function setAIPreference(preferOnline) {
    try {
        const response = await fetch('http://localhost:3000/api/ai/preference', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ preferOnline }),
        });

        if (response.ok) {
            const data = await response.json();
            aiSystemStatus.preferOnline = data.preferOnline;
            console.log(`AI preference updated to: ${preferOnline ? 'Online' : 'Offline'}`);

            // Refresh status after preference change
            setTimeout(() => getAISystemStatus(), 1000);

            return data;
        }
    } catch (error) {
        console.error('Failed to set AI preference:', error);
    }
    return null;
}

// Listen for custom events from the main application
document.addEventListener('stressDataUpdated', (event) => {
    if (event.detail && event.detail.prediction) {
        updateStressData(event.detail.prediction, event.detail.studentData);
    }
});

const websim = {
    // Store and access student stress data
    stressData: studentStressData,
    updateStressData: updateStressData,

    // AI System management
    aiStatus: aiSystemStatus,
    getAISystemStatus: getAISystemStatus,
    setAIPreference: setAIPreference,

    chat: {
        completions: {
            create: async function(options) {
                try {
                    // Get current AI status before making request
                    await getAISystemStatus();

                    // Add stress data to the request if available
                    const requestBody = {
                        messages: options.messages,
                        json: options.json || false
                    };

                    // Include stress data if available
                    if (studentStressData.lastPrediction) {
                        requestBody.stressData = studentStressData;
                    }

                    const response = await fetch('http://localhost:3000/api/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody),
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`AI System Error: ${errorData.error || response.statusText}`);
                    }

                    const result = await response.json();

                    // Log which model was used
                    if (result.model) {
                        console.log(`Response from: ${result.model}`);

                        // Update UI with current model info
                        document.dispatchEvent(new CustomEvent('aiResponseReceived', {
                            detail: { model: result.model, usage: result.usage }
                        }));
                    }

                    return result;
                } catch (error) {
                    console.error('Error in websim.chat.completions.create:', error);
                    throw error;
                }
            }
        }
    }
};

// Initialize AI system status on load
document.addEventListener('DOMContentLoaded', () => {
    console.log('🤖 Dual AI System Client loaded');
    console.log('   📡 Online: DeepSeek via OpenRouter');
    console.log('   💻 Offline: Llama3.2:1b via Ollama');

    // Get initial AI system status
    getAISystemStatus();

    // Periodically check AI system status (every 60 seconds)
    setInterval(getAISystemStatus, 60000);
});

console.log('Websim loaded - Dual AI System (DeepSeek Online + Llama3.2 Offline)');
