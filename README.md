# Student Stress Predictor AI with Ollama Integration
Yoooo  Check
This project is a web application that helps students predict and manage their stress levels. It now includes integration with Ollama to use your local Llama 3.2 (1B) model.

## Setup Instructions

### Prerequisites
- Node.js and npm installed
- Ollama installed and running (https://ollama.com/)
- Llama 3.2 (1B parameter version) model pulled in Ollama

### Step 1: Install Ollama
If you haven't already, install Ollama from https://ollama.com/

### Step 2: Pull the Llama 3.2 (1B) model
Run the following command to pull the Llama 3.2 (1B) model:
```
ollama pull llama3.2:1b
```

### Step 3: Install dependencies
Run the following command in the project directory:
```
npm install
```

### Step 4: Start the server
Run the following command to start the server:
```
npm start
```

### Step 5: Open the application
Open your browser and navigate to:
```
http://localhost:3000
```

## Configuration

If your Ollama instance is running on a different port or if you want to use a different model, you can modify the `server.js` file:

```javascript
// Configuration for Ollama
const OLLAMA_BASE_URL = 'http://localhost:11434'; // Change if needed
const MODEL_NAME = 'llama3.2:1b'; // Change to your model name if using a different one
```

## Features
- Stress prediction based on various factors
- AI chat assistant powered by your local Llama 3.2 (1B) model
- Stress relief activities
- Stress history tracking
- User profile customization
